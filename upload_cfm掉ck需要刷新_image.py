import requests

# API 端点 URL
url = "https://cfm.game.qq.com/adm/cf/index.php/api/uploadAppImg/uploadimg"

def upload_cfm_image(image_file_path):
    headers = {
    }

    try:
        with open(image_file_path, 'rb') as f:
            files_payload = {
                'file': (image_file_path.split('/')[-1].split('\\')[-1], f, 'image/gif'), # 明确指定 Content-Type
                'openid': (None, '166969CF12D6688B67123FD98AF16DB5') 
            }
            
            # requests 库会自动处理 Content-Type (multipart/form-data with boundary) 和 Content-Length
            response = requests.post(url, headers=headers, files=files_payload)
            
            print(f"响应状态码: {response.status_code}")
            response.raise_for_status()

            response_json = response.json()
            print("响应内容 (JSON):")
            print(response_json)

            if response_json.get("code") == 200 and response_json.get("data", {}).get("data"):
                img_data = response_json["data"]["data"]
                print("\n图片上传成功!")
                print(f"  大图 URL: {img_data.get('big')}")
                print(f"  小图 URL: {img_data.get('small')}")
            else:
                print(f"\n图片上传失败或响应格式不符合预期: {response_json.get('msg', '未知错误')}")

    except FileNotFoundError:
        print(f"错误：文件 '{image_file_path}' 未找到。")
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP 错误: {http_err}")
        print(f"响应文本: {response.text if response else 'N/A'}")
    except requests.exceptions.RequestException as req_err:
        print(f"请求发生错误: {req_err}")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    image_file_path = input("请输入要上传的图片文件的完整路径 (例如 psc.gif): ")
    upload_cfm_image(image_file_path)