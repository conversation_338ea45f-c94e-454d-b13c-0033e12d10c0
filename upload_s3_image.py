import requests

# 新的请求 URL
url = "https://www.sou.com/api/s3/upload"

def upload_s3_image(image_path):
    # 请求头 (已根据用户测试进一步精简)
    headers = {
       
        "access-token": "15121785522587418649107282001748",
        "device-platform": "Web",
        "origin": "https://www.sou.com",
        "referer": "https://www.sou.com/?src=se_platform",
        "timestamp": "2025-05-28T12:00:16+08:00", 
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
        "zm-token": "8a95fecf59bb48dd74bc9c68d760f1a6",
        "zm-ua": "99d149899c4f2f3d79df1f8e73f539ef",
        "zm-ver": "1.2"
    }

    try:
        with open(image_path, 'rb') as f:
            files = {
                'up_file': (image_path.split('/')[-1].split('\\')[-1], f)
            }
            response = requests.post(url, headers=headers, files=files)
            print(f"响应状态码: {response.status_code}")
            try:
                response_json = response.json()
                print("响应内容 (JSON):")
                print(response_json)
                if response_json.get("code") == 0 and response_json.get("data", {}).get("up_url"):
                    print(f"图片上传成功！图片URL: {response_json['data']['up_url']}")
                else:
                    print(f"图片上传失败: {response_json.get('msg', '未知错误')}")
            except requests.exceptions.JSONDecodeError:
                print("响应内容 (非JSON):")
                print(response.text)

    except FileNotFoundError:
        print(f"错误：文件 '{image_path}' 未找到。")
    except Exception as e:
        print(f"上传过程中发生错误: {e}")

if __name__ == "__main__":
    image_file_path = input("请输入要上传的图片文件的完整路径: ")
    upload_s3_image(image_file_path) 