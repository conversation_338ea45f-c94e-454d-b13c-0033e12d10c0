import requests
import os

# IconPark 头像上传接口
url = "https://iconpark.oceanengine.com/web/user/update/upload_avatar/"

def upload_iconpark_avatar(image_path, aid="3355"):
    # 请求头 (根据IconPark头像上传接口要求)
    headers = {
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "origin": "https://iconpark.oceanengine.com",
        "priority": "u=1, i",
        "referer": "https://iconpark.oceanengine.com/projects",
        "sec-ch-ua": '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "x-secsdk-csrf-token": "DOWNGRADE",
        # 精简后的cookie信息（保留认证相关的核心cookie）
        "cookie": "passport_csrf_token=a3f221f7af87bdb2ce3017e9be4e0001; passport_csrf_token_default=a3f221f7af87bdb2ce3017e9be4e0001; is_staff_user_okee=false; ttwid=1%7Cd7iUMx3hFah1T8h67Az3ALNE40px5Cfr13Yuz95ug-A%7C1744710009%7C29d45ed862a6d2bc246107ba6e7215d653afd714c335812220f88b4ea9849e0b; n_mh_okee=9-mIeuD4wZnlYrrOvfzG3MuT6aQmCUtmr8FxV8Kl8xY; sid_guard_okee=dadc5be6c16294377d0f77ca105cfd9a%7C1749004408%7C5184000%7CSun%2C+03-Aug-2025+02%3A33%3A28+GMT; uid_tt_okee=d9c95c7b93ea65d53920fa533a447de2; uid_tt_ss_okee=d9c95c7b93ea65d53920fa533a447de2; sid_tt_okee=dadc5be6c16294377d0f77ca105cfd9a; sessionid_okee=dadc5be6c16294377d0f77ca105cfd9a; sessionid_ss_okee=dadc5be6c16294377d0f77ca105cfd9a; sid_ucp_v1_okee=1.0.0-KGYwNWRjYzUwNTY2OTdiNDhhYTYxYmM1NzRhZDU2OWVjZjc0MjI4NGEKHgicorDYv6zIAhD44P7BBhibGiAMMPfg_sEGOAhAOBoCbHEiIGRhZGM1YmU2YzE2Mjk0Mzc3ZDBmNzdjYTEwNWNmZDlh; ssid_ucp_v1_okee=1.0.0-KGYwNWRjYzUwNTY2OTdiNDhhYTYxYmM1NzRhZDU2OWVjZjc0MjI4NGEKHgicorDYv6zIAhD44P7BBhibGiAMMPfg_sEGOAhAOBoCbHEiIGRhZGM1YmU2YzE2Mjk0Mzc3ZDBmNzdjYTEwNWNmZDlh; s_v_web_id=verify_mbhaoysh_lHoJFimy_xNMI_4cfn_97pZ_y0o2SMWhZXSL"
    }

    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误：文件 '{image_path}' 未找到。")
            return

        # 获取文件名和扩展名
        filename = os.path.basename(image_path)
        file_ext = os.path.splitext(filename)[1].lower()
        
        # 根据文件扩展名设置Content-Type
        content_type_map = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp',
            '.ico': 'image/x-icon'
        }
        content_type = content_type_map.get(file_ext, 'application/octet-stream')
        
        with open(image_path, 'rb') as f:
            file_content = f.read()
            
            # 根据原始请求格式构建multipart数据
            files = {
                'avatar': (filename, file_content, content_type)  # 字段名为'avatar'
            }
            
            # 添加aid参数
            data = {
                'aid': aid
            }
            
            print(f"正在上传头像文件: {filename}")
            print(f"文件类型: {content_type}")
            print(f"AID参数: {aid}")
            
            response = requests.post(url, headers=headers, files=files, data=data)
            print(f"响应状态码: {response.status_code}")
            
            try:
                response_json = response.json()
                print("响应内容 (JSON):")
                print(response_json)
                
                # 根据IconPark头像接口的响应格式判断成功状态
                if response.status_code == 200 and response_json.get("message") == "success":
                    print(f"头像上传成功！")
                    data = response_json.get("data", {})
                    if data.get("web_uri"):
                        image_url = data["web_uri"]
                        print(f"头像URL: {image_url}")
                    else:
                        print("响应中未找到头像URL")
                        print("完整响应:", response_json)
                else:
                    print(f"头像上传失败: {response_json.get('message', '未知错误')}")
                    
            except requests.exceptions.JSONDecodeError:
                print("响应内容 (非JSON):")
                print(response.text)
                if response.status_code == 200:
                    print("虽然响应不是JSON格式，但状态码为200，可能上传成功")

    except FileNotFoundError:
        print(f"错误：文件 '{image_path}' 未找到。")
    except Exception as e:
        print(f"上传过程中发生错误: {e}")

def set_custom_headers(custom_headers):
    """
    设置自定义请求头的辅助函数
    使用方法: set_custom_headers({"cookie": "your_cookie_here"})
    """
    global headers
    if 'headers' in globals():
        headers.update(custom_headers)
        print("请求头已更新")
    else:
        print("请先运行upload_iconpark_avatar函数")

def set_aid(new_aid):
    """
    设置AID参数的辅助函数
    使用方法: set_aid("your_aid_here")
    """
    print(f"AID参数已设置为: {new_aid}")
    return new_aid

if __name__ == "__main__":
    print("IconPark 头像上传工具")
    print("⚠️  注意：经测试主要支持GIF格式")
    print("✅ 确认支持: GIF")
    print("❓ 其他格式可能不被支持")
    print("📝 默认AID: 3355 (可通过set_aid函数修改)")
    print("如需添加cookie或其他请求头，请调用 set_custom_headers({'key': 'value'})")
    print("-" * 60)
    
    # 询问是否需要修改AID
    aid_input = input("请输入AID参数 (直接回车使用默认值3355): ").strip()
    aid = aid_input if aid_input else "3355"
    
    image_file_path = input("请输入要上传的头像文件的完整路径: ")
    upload_iconpark_avatar(image_file_path, aid)
