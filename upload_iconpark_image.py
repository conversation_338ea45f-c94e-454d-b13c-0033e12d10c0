import requests
import os

# IconPark 图片上传接口
url = "https://iconpark.oceanengine.com/api/tool/upload/logo"

def upload_iconpark_image(image_path):
    # 请求头 (根据IconPark接口要求，包含完整的认证信息)
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "origin": "https://iconpark.oceanengine.com",
        "priority": "u=1, i",
        "referer": "https://iconpark.oceanengine.com/projects/34519/detail",
        "sec-ch-ua": '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "x-secsdk-csrf-token": "DOWNGRADE",
        # 完整的cookie信息
        "cookie": "MONITOR_WEB_ID=3fa09862-ba14-4f4b-a0e7-170cdc6eca05; _ga=GA1.1.1135302409.1743416177; passport_csrf_token=a3f221f7af87bdb2ce3017e9be4e0001; passport_csrf_token_default=a3f221f7af87bdb2ce3017e9be4e0001; n_mh_okee=yzQTzFAPVSs09VinPFRBrslyBF8_f2Hqv-Gz4sZgr0g; sid_guard_okee=6d3181bb178ef71ec8f4303b8a05d33b%7C1744453682%7C5184000%7CWed%2C+11-Jun-2025+10%3A28%3A02+GMT; uid_tt_okee=ddd64da945430379a18abb5a3277237a; uid_tt_ss_okee=ddd64da945430379a18abb5a3277237a; sid_tt_okee=6d3181bb178ef71ec8f4303b8a05d33b; sessionid_okee=6d3181bb178ef71ec8f4303b8a05d33b; sessionid_ss_okee=6d3181bb178ef71ec8f4303b8a05d33b; is_staff_user_okee=false; sid_ucp_v1_okee=1.0.0-KGMzYWIxNDUzNzU3MDgyMjJkZmI1MmFlMGM2YzExODk0MjBkZTBlOTEKFgia-vDjrM1JELKA6b8GGJsaOAJA8QcaAmxxIiA2ZDMxODFiYjE3OGVmNzFlYzhmNDMwM2I4YTA1ZDMzYg; ssid_ucp_v1_okee=1.0.0-KGMzYWIxNDUzNzU3MDgyMjJkZmI1MmFlMGM2YzExODk0MjBkZTBlOTEKFgia-vDjrM1JELKA6b8GGJsaOAJA8QcaAmxxIiA2ZDMxODFiYjE3OGVmNzFlYzhmNDMwM2I4YTA1ZDMzYg; ttwid=1%7Cd7iUMx3hFah1T8h67Az3ALNE40px5Cfr13Yuz95ug-A%7C1744710009%7C29d45ed862a6d2bc246107ba6e7215d653afd714c335812220f88b4ea9849e0b; _ga_313DD262YW=GS1.1.1744709865.1.1.*********4.0.0.0; s_v_web_id=verify_mbhaoysh_lHoJFimy_xNMI_4cfn_97pZ_y0o2SMWhZXSL; _ga_L06HKX6C3X=GS2.1.s1749002103$o8$g1$t1749002183$j59$l0$h0"
    }

    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误：文件 '{image_path}' 未找到。")
            return

        # 获取文件名和扩展名
        filename = os.path.basename(image_path)
        file_ext = os.path.splitext(filename)[1].lower()
        
        # 根据文件扩展名设置Content-Type
        content_type_map = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp',
            '.ico': 'image/x-icon'
        }
        content_type = content_type_map.get(file_ext, 'application/octet-stream')
        
        with open(image_path, 'rb') as f:
            file_content = f.read()
            files = {
                'file': (filename, file_content, content_type)  # 根据IconPark接口要求，字段名为'file'
            }
            
            print(f"正在上传文件: {filename}")
            print(f"文件类型: {content_type}")
            response = requests.post(url, headers=headers, files=files)
            print(f"响应状态码: {response.status_code}")
            
            try:
                response_json = response.json()
                print("响应内容 (JSON):")
                print(response_json)
                
                # 根据IconPark接口的响应格式判断成功状态
                # 通常成功的响应会包含上传后的URL信息
                if response.status_code == 200:
                    # 检查不同可能的成功标识
                    if (response_json.get("code") == 0 or 
                        response_json.get("status") == "success" or 
                        response_json.get("success") == True or
                        "url" in response_json or
                        "data" in response_json):
                        
                        print(f"图片上传成功！")
                        
                        # 尝试提取URL信息
                        image_url = None
                        if "url" in response_json:
                            image_url = response_json["url"]
                        elif "data" in response_json and isinstance(response_json["data"], dict):
                            data = response_json["data"]
                            image_url = data.get("url") or data.get("path") or data.get("link")
                        elif "data" in response_json and isinstance(response_json["data"], str):
                            image_url = response_json["data"]
                        
                        if image_url:
                            # 处理相对URL
                            if image_url.startswith("//"):
                                image_url = "https:" + image_url
                            elif image_url.startswith("/"):
                                image_url = "https://iconpark.oceanengine.com" + image_url
                            print(f"图片URL: {image_url}")
                        else:
                            print("响应中未找到图片URL，但上传可能成功")
                            print("完整响应:", response_json)
                    else:
                        print(f"图片上传失败: {response_json.get('message', response_json.get('msg', '未知错误'))}")
                else:
                    print(f"请求失败，状态码: {response.status_code}")
                    
            except requests.exceptions.JSONDecodeError:
                print("响应内容 (非JSON):")
                print(response.text)
                if response.status_code == 200:
                    print("虽然响应不是JSON格式，但状态码为200，可能上传成功")

    except FileNotFoundError:
        print(f"错误：文件 '{image_path}' 未找到。")
    except Exception as e:
        print(f"上传过程中发生错误: {e}")

def set_custom_headers(custom_headers):
    """
    设置自定义请求头的辅助函数
    使用方法: set_custom_headers({"cookie": "your_cookie_here"})
    """
    global headers
    if 'headers' in globals():
        headers.update(custom_headers)
        print("请求头已更新")
    else:
        print("请先运行upload_iconpark_image函数")

if __name__ == "__main__":
    print("IconPark SVG图标上传工具")
    print("⚠️  注意：此接口主要用于上传SVG格式的图标文件")
    print("✅ 推荐格式: SVG")
    print("❓ 其他格式可能不被支持")
    print("如需添加cookie或其他请求头，请调用 set_custom_headers({'key': 'value'})")
    print("-" * 60)
    
    image_file_path = input("请输入要上传的图片文件的完整路径: ")
    upload_iconpark_image(image_file_path)
