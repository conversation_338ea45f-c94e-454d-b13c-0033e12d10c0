import requests

# 网易严选文件上传接口
url = "https://you.163.com/xhr/file/upload.json"

def upload_youdao_image(image_path):
    # 请求头 (极简版本)
    headers = {
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0"
    }

    try:
        with open(image_path, 'rb') as f:
            files = {
                'file': (image_path.split('/')[-1].split('\\')[-1], f)
            }
            response = requests.post(url, headers=headers, files=files)
            print(f"响应状态码: {response.status_code}")
            try:
                response_json = response.json()
                print("响应内容 (JSON):")
                print(response_json)
                # 根据网易严选接口的响应格式判断成功状态
                if response.status_code == 200 and response_json.get("code") == "200":
                    print(f"图片上传成功！")
                    # 处理data字段，它可能是列表格式
                    data = response_json.get("data")
                    if data:
                        if isinstance(data, list) and len(data) > 0:
                            image_url = data[0]  # 取列表中第一个URL
                            print(f"图片URL: {image_url}")
                        elif isinstance(data, dict) and data.get("url"):
                            image_url = data.get("url")
                            print(f"图片URL: {image_url}")
                        elif isinstance(data, str):
                            print(f"图片URL: {data}")
                else:
                    print(f"图片上传失败: {response_json.get('msg', response_json.get('message', '未知错误'))}")
            except requests.exceptions.JSONDecodeError:
                print("响应内容 (非JSON):")
                print(response.text)

    except FileNotFoundError:
        print(f"错误：文件 '{image_path}' 未找到。")
    except Exception as e:
        print(f"上传过程中发生错误: {e}")

if __name__ == "__main__":
    image_file_path = input("请输入要上传的图片文件的完整路径: ")
    upload_youdao_image(image_file_path) 