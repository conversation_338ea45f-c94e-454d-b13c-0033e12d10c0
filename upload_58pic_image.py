import requests
import os

# 58pic.com 图片上传接口
url = "https://api-upload.58pic.com/index.php?c=qt_file&a=upyunqt"

def upload_58pic_image(image_path):
    # 请求头 (根据58pic.com接口要求)
    headers = {
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "connection": "keep-alive",
        "host": "api-upload.58pic.com",
        "origin": "https://www.58pic.com",
        "referer": "https://www.58pic.com/index.php?m=HelpCenter&a=addQuestion",
        "sec-ch-ua": '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        # 注意：实际使用时需要替换为有效的cookie
        "cookie": "qt_visitor_id=%226ade10fe86db874e405fd107836ac753%22; qt_type=0; new-old-switch=new; did=%2239104a80f6f325e6%22; history_did_data_39104a80f6f325e6=%22eyJkaXN0aW5jdF9pZCI6IjM5MTA0YTgwZjZmMzI1ZTYiLCJ1dG1fY2FtcGFpZ24iOjAsInV0bV9zb3VyY2UiOjAsInV0bV9tZWRpdW0iOjAsInV0bV90ZXJtIjowLCJ1dG1fY29udGVudCI6MCwidGlkIjowfQ%3D%3D%22; qiantudata2018jssdkcross=%7B%22distinct_id%22%3A%22197351c108813d-0509aed84838328-4c657b58-2073600-197351c108a5da%22%7D; FIRSTVISITED=1748942721.322; tid_today_data_39104a80f6f325e6_20250603=%22eyJub1RvZGF5RGlkIjoxfQ%3D%3D%22; history_uid_data_83029456=%22eyJ1aWQiOjgzMDI5NDU2LCJkaXN0aW5jdF9pZCI6IjM5MTA0YTgwZjZmMzI1ZTYiLCJ1dG1fY2FtcGFpZ24iOjAsInV0bV9zb3VyY2UiOjAsInV0bV9tZWRpdW0iOjAsInV0bV90ZXJtIjowLCJ1dG1fY29udGVudCI6MCwiZmlyc3RfdHJhZmZpY19zb3VyY2VfdHlwZSI6MCwidGlkIjowfQ%3D%3D%22; success_target_path=%22https%3A%5C%2F%5C%2Fwww.58pic.com%5C%2F%22; last_login_type=1; qt_risk_visitor_id=%22f0203989a7a4e297711995e08c7c2f3a%22; _is_pay=0; risk_forbid_login_uid=%2283029456%22; auth_id_list=6rc_1Z4BQpTOV7R5x44XEzK484AnnO_qRFu0A6ViFejCdEJ8RVdcIfZujRgvMmtFmfwA153mvm84RF_o1foDcmP_NUc0_Gm40LqVCVEDb9cfEuluY7ByFf8kj8FpAZWK9RJl-mfRjIzBzY5p-AOdeqEazM-dUrlQxTvbB4u3I8833AajTIR9izl0kDZbc5WHQ_SBgA39olviJLBHIuffRsFTTvkEmUVTQxigITdM5y600sf4JJBFbvbXvA13zbUh2usJvyDEMkBEBBUCOM-E8Dc1HddexuCNkml_rWS3XK1_lVOCTtiD7ovgQWw7y6dTeDamELN8nJCql5Vj-S17b_Zw3_yj09euOb0tjT3kEgCWJP407aB-6HRZrOQMVC1PBHO_rTUlsHxEdYO6GlCEVEodyd_5339YMpTKAN_u4d70vno5tQRr8AKFvtDXBxC9ja3FaUnbQEAwlVBuoAGiF2utL9x0qORKT2Hn2VyXxD8qX5FFjHMpfLCUvPtlkqRgVA7M1FRxFe2mG0i_dY6U7eX10nzHX4gBFTgs6AU2MfJefanm672pkksLoq6l4xWtUKiD9S2u9zICgWCYErVHaCjyO3b9mbqsAGiFSdcMZGzMJDqJKiH9N1f5LRVNWpS-; sns=%7B%22token%22%3A%7B%22access_token%22%3A%22C6F979669E4B69861FDCFA83AADD3F03%22%2C%22expires_in%22%3A%225184000%22%2C%22refresh_token%22%3A%2236FEDD5A2522F439185AF79DEEFB7948%22%2C%22openid%22%3A%222FB93B14476EB7F28B74605A2B853F0B%22%7D%2C%22type%22%3A%22qq%22%7D; ssid=%22683ebf90ae5303.89044485%22; newbieTask=%22%7B%5C%22is_login%5C%22%3A%5C%221%5C%22%2C%5C%22is_search%5C%22%3A%5C%220%5C%22%2C%5C%22is_download%5C%22%3A%5C%220%5C%22%2C%5C%22is_keep%5C%22%3A%5C%220%5C%22%2C%5C%22login_count%5C%22%3A%5C%221%5C%22%2C%5C%22upload_material%5C%22%3A%5C%220%5C%22%2C%5C%22before_login_time%5C%22%3A%5C%221748880000%5C%22%2C%5C%22is_task_complete%5C%22%3A%5C%220%5C%22%2C%5C%22task1%5C%22%3A%5C%220%5C%22%2C%5C%22task2%5C%22%3A%5C%220%5C%22%2C%5C%22task3%5C%22%3A%5C%220%5C%22%7D%22; _auth_dl_=ODMwMjk0NTZ8MTc0OTU0NzUzNnw1ZmQyZjc5ZGMyMzliNTg1MTgxYjA1YzY1NzZkOTlkZQ%3D%3D; auth_id_v2=MNaMcJR_1-bTQEcuZlPRIP4s33sRBFHXdcogAERMqoY5RqOovywenlnq4qxKeXIsmVAjNNZmaj3_KcgEzB9DCxxuaD8Bs3Ed94f0D-PNCGJ99CZszjqUHxC14FbTY6TuEIyGvssu0DmLCT5EfRYJQ0HDWqBZGBXCD3fvKAjTCoxkHMgy2N09-moL5S2pvF7eRGr9Qhlq6tP2cMaTlRujm6UmLLU-BIjcART2peafixwJe5l2woDSZbvRNuNH3lUBeBA964GoPO6RD4Brv-yiboBrx2kwYi9g2aFrQcwtRAMrxAYD7sweOvd0lEJlSy-j; auth_id=%2283029456%7C5Y2D5Zu%2B55So5oi3Xzk0NTY%3D%7C1750152348%7C0a5cf567f79da572c11c3947174f28af%22; login_status=1; qt_uid=%2283029456%22; ISREQUEST=1; WEBPARAMS=is_pay=0; tid_today_data_39104a80f6f325e6_20250604=%22eyJub1RvZGF5RGlkIjoxfQ%3D%3D%22; public_property=%22eyJ1aWQiOiI4MzAyOTQ1NiIsImxpYiI6InBocCIsImxpYl92ZXJzaW9uIjoiMS4wIiwiZXF1aXAiOjEsImRpc3RpbmN0X2lkIjoiMzkxMDRhODBmNmYzMjVlNiIsImV2ZW50X25hbWUiOiIiLCJzZXJ2ZXJfYWdlbnQiOiJNb3ppbGxhXC81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXRcLzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZVwvMTM1LjAuMC4wIFNhZmFyaVwvNTM3LjM2IEVkZ1wvMTM1LjAuMC4wIiwidXJsIjoiaHR0cDpcL1wvd3d3LjU4cGljLmNvbVwvIiwidGltZSI6MTc0OTAwMDYyOCwiY2xpZW50X2lwIjoiMTcxLjExLjczLjIzNiIsIm9zIjoiV2luZG93cyAxMCIsImJyb3dzZXIiOiJDaHJvbWUiLCJicm93c2VyX3ZlcnNpb24iOiIxMzUuMC4wLjAiLCJyZWZlcnJlciI6Imh0dHBzOlwvXC93d3cuYmluZy5jb21cLyIsImxhdGVzdF90cmFmZmljX3NvdXJjZV90eXBlIjpudWxsLCJsYXRlc3RfcmVmZXJyZXIiOm51bGwsImxhdGVzdF9yZWZlcnJlcl9ob3N0IjpudWxsLCJsYXRlc3Rfc2VhcmNoX2tleXdvcmQiOm51bGwsImxhdGVzdF91dG1fbWVkaXVtIjpudWxsLCJsYXRlc3RfdXRtX2NhbXBhaWduIjpudWxsLCJsYXRlc3RfdXRtX3Rlcm0iOm51bGwsImxhdGVzdF91dG1fc291cmNlIjpudWxsLCJsYXRlc3RfdGlkIjpudWxsLCJsYXRlc3RfdXRtX2NvbnRlbnQiOm51bGwsInF5X2lkIjowLCJ1c2VyX3N0YXR1cyI6MSwidGlkIjowLCJ1dG1fc291cmNlIjowLCJ1dG1fbWVkaXVtIjowLCJ1dG1fY2FtcGFpZ24iOjAsInV0bV9jb250ZW50IjowLCJ1dG1fdGVybSI6MH0%3D%22; han_data_is_pay:83029456=2; tfstk=grfIaLMdIDmB9SO-Pp4Z1bIlokA77PP4O4TRoahEyBdK20QvbvnJa9-5CgIMLHBFzUG5-h-P4Q55Ib_cr6zHayAh-QA80oPVVwbHZg-oPcENWzLkNe3Gnt7V-QA8bscVUR7hxF4_MMd85CLJl0d-pbU6We-t9UhpycU6jhL-pQKJXhL2RX3d2QU1WhYJw3IJpP_ttO3XPJthRyudo5wSgH7pfbhR97vBcaDrwbC6Rp1RJh6Re1TBdnpJENDF9G7RT6vg3xAl7ts5eGFsOg_dHgKF6ohW4tfhW19jyb7613_DvIg89NOBAKCNxShPDN_R3d1akSAB9HBytaubYN1CYwf1z4H9OBW6h6OYiDKPQZ1WP_qu1i_dHgB54Sh2cRnnFV9mNFt45PMoE-SqINKi7wgBpFYBQPasvTvpSFt45PMoEpLMRhz_5DBl.; PHPSESSID=lmbadp8f11aimu9ep8nsmko0c0; IPSSESSION=8enjkbh9lalcc875ipsamqolc3; ui_58pic=dWlkPTgzMDI5NDU2JnVjPTIwMjUtMDYtMDQgMDk6MzM6MDImdj0xJnVzPSZ0PWMwMTEyOTdiMTIxZjczNDE0OTMwMzI4M2VmNTA5YzE3MTc0OTAwMDc4Mi4xMzE4MjY1OTYmZ3I9MSZ1cnM9; track_id=35044e6ac75719209fae1ff1224e9d2a401678f753513cca41a4a2a5c458da50a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22track_id%22%3Bi%3A1%3Bs%3A52%3A%22c011297b121f734149303283ef509c171749000782.131826596%22%3B%7D; qt_utime=1749000790; big_data_visit_time=1749000790"
    }

    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误：文件 '{image_path}' 未找到。")
            return

        # 获取文件名
        filename = os.path.basename(image_path)
        
        with open(image_path, 'rb') as f:
            files = {
                'file': (filename, f, 'image/gif')  # 根据58pic接口要求，字段名为'file'
            }
            
            print(f"正在上传文件: {filename}")
            response = requests.post(url, headers=headers, files=files)
            print(f"响应状态码: {response.status_code}")
            
            try:
                response_json = response.json()
                print("响应内容 (JSON):")
                print(response_json)
                
                # 根据58pic接口的响应格式判断成功状态
                if response_json.get("status") == True and response_json.get("msg") == "success":
                    print(f"图片上传成功！")
                    data = response_json.get("data", {})
                    if data.get("url"):
                        # 处理URL，如果是相对路径则添加协议
                        image_url = data["url"]
                        if image_url.startswith("//"):
                            image_url = "https:" + image_url
                        print(f"图片URL: {image_url}")
                        print(f"文件路径: {data.get('path', '')}")
                        print(f"原始文件名: {data.get('filename', '')}")
                    else:
                        print("响应中未找到图片URL")
                else:
                    print(f"图片上传失败: {response_json.get('msg', '未知错误')}")
                    
            except requests.exceptions.JSONDecodeError:
                print("响应内容 (非JSON):")
                print(response.text)

    except FileNotFoundError:
        print(f"错误：文件 '{image_path}' 未找到。")
    except Exception as e:
        print(f"上传过程中发生错误: {e}")

def update_cookie(new_cookie):
    """
    更新cookie的辅助函数
    使用方法: update_cookie("你的新cookie字符串")
    """
    global headers
    if 'headers' in globals():
        headers['cookie'] = new_cookie
        print("Cookie已更新")
    else:
        print("请先运行upload_58pic_image函数")

if __name__ == "__main__":
    print("58pic.com 图片上传工具")
    print("注意：使用前请确保cookie信息是最新的，否则可能上传失败")
    print("如需更新cookie，请调用 update_cookie('新的cookie字符串')")
    print("-" * 50)
    
    image_file_path = input("请输入要上传的图片文件的完整路径: ")
    upload_58pic_image(image_file_path)
