import requests

# API 端点 URL
url = "https://bot.n.cn/api/v1/aliy/assumerole"

def assume_aliy_role():
    # 根据您提供的信息构建请求头 (进一步精简)
    headers = {
       
        "access-token": "25170478133103499604803100017484",
        "device-platform": "Web",   
        "origin": "https://bot.n.cn",
        "referer": "https://bot.n.cn/?src=dh_3rd",
        "timestamp": "2025-05-28T17:24:48+08:00",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Firefox/123.0",
        "zm-token": "8f44b6af76fbe4169cda02009c104f08",
        "zm-ua": "0e0369e2813db7deb26e5937c353aab4",
        "zm-ver": "1.2"
    }

    try:
        # 发送 POST 请求，因为 content-length 为 0，所以不传递 data 或 json 参数
        response = requests.post(url, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        response.raise_for_status() # 如果状态码不是 2xx，则抛出异常

        # 打印响应内容 (JSON格式)
        response_json = response.json()
        print("响应内容 (JSON):")
        print(response_json)

        if response_json.get("code") == 0 and "data" in response_json:
            print("\n成功获取临时凭证:")
            credentials = response_json["data"]
            print(f"  Access Key ID: {credentials.get('access_key_id')}")
            print(f"  Access Key Secret: {credentials.get('access_key_secret')}")
            print(f"  Security Token: {credentials.get('security_token')}")
            print(f"  Expiration: {credentials.get('expiration')}")
        else:
            print(f"\n获取凭证失败或响应格式不符合预期: {response_json.get('msg', '未知错误')}")

    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP 错误: {http_err}")
        print(f"响应文本: {response.text}")
    except requests.exceptions.RequestException as req_err:
        print(f"请求发生错误: {req_err}") # 包含了 json decode error
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    assume_aliy_role()